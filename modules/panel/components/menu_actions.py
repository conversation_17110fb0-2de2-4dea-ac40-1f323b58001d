"""
Menu action handlers for the macOS-style menu bar.
This file contains all the implementations for menu item actions.
"""

from fabric.hyprland.service import <PERSON>yp<PERSON>
from fabric.utils import exec_shell_command


class MenuActionHandler:
    """Handler class for menu actions"""
    
    def __init__(self):
        pass
    
    def execute_action(self, action):
        """Execute the actual functionality for menu actions"""
        try:
            # Window menu actions
            if action == "Move Window to Left":
                reply = Hyprland.send_command("dispatch movewindow l")
                print(f"Move left result: {reply.reply.decode()}")
            elif action == "Move Window to Right":
                reply = Hyprland.send_command("dispatch movewindow r")
                print(f"Move right result: {reply.reply.decode()}")
            elif action == "Cycle Through Windows":
                reply = Hyprland.send_command("dispatch cyclenext")
                print(f"Cycle windows result: {reply.reply.decode()}")
            elif action == "Float":
                reply = Hyprland.send_command("dispatch togglefloating")
                print(f"Toggle float result: {reply.reply.decode()}")
            elif action == "Pseudo":
                reply = Hyprland.send_command("dispatch pseudo")
                print(f"Pseudo result: {reply.reply.decode()}")
            elif action == "Center":
                reply = Hyprland.send_command("dispatch centerwindow")
                print(f"Center window result: {reply.reply.decode()}")
            elif action == "Group":
                reply = Hyprland.send_command("dispatch togglegroup")
                print(f"Toggle group result: {reply.reply.decode()}")
            elif action == "Pin":
                reply = Hyprland.send_command("dispatch pin")
                print(f"Pin result: {reply.reply.decode()}")
            elif action == "Quit":
                reply = Hyprland.send_command("dispatch killactive")
                print(f"Kill active result: {reply.reply.decode()}")

            # Go menu actions
            elif action == "Back":
                reply = Hyprland.send_command("dispatch workspace e-1")
                print(f"Go back result: {reply.reply.decode()}")
            elif action == "Forward":
                reply = Hyprland.send_command("dispatch workspace e+1")
                print(f"Go forward result: {reply.reply.decode()}")
            elif action == "Home":
                result = exec_shell_command("xdg-open ~")
                print(f"Open home result: {result}")
            elif action == "Desktop":
                result = exec_shell_command("xdg-open ~/Desktop")
                print(f"Open desktop result: {result}")
            elif action == "Documents":
                result = exec_shell_command("xdg-open ~/Documents")
                print(f"Open documents result: {result}")
            elif action == "Downloads":
                result = exec_shell_command("xdg-open ~/Downloads")
                print(f"Open downloads result: {result}")

            # Edit menu actions
            elif action == "Undo":
                # Send Ctrl+Z to the active window
                result = exec_shell_command("wtype -k ctrl+z")
                print(f"Undo result: {result}")
            elif action == "Redo":
                # Send Ctrl+Y to the active window
                result = exec_shell_command("wtype -k ctrl+y")
                print(f"Redo result: {result}")
            elif action == "Cut":
                # Send Ctrl+X to the active window
                result = exec_shell_command("wtype -k ctrl+x")
                print(f"Cut result: {result}")
            elif action == "Copy":
                # Send Ctrl+C to the active window
                result = exec_shell_command("wtype -k ctrl+c")
                print(f"Copy result: {result}")
            elif action == "Paste":
                # Send Ctrl+V to the active window
                result = exec_shell_command("wtype -k ctrl+v")
                print(f"Paste result: {result}")
            elif action == "Select All":
                # Send Ctrl+A to the active window
                result = exec_shell_command("wtype -k ctrl+a")
                print(f"Select All result: {result}")
            elif action == "Find":
                # Send Ctrl+F to the active window
                result = exec_shell_command("wtype -k ctrl+f")
                print(f"Find result: {result}")
            elif action == "Replace":
                # Send Ctrl+H to the active window
                result = exec_shell_command("wtype -k ctrl+h")
                print(f"Replace result: {result}")

            # View menu actions
            elif action == "Full Screen":
                # Toggle fullscreen for the active window
                reply = Hyprland.send_command("dispatch fullscreen")
                print(f"Toggle fullscreen result: {reply.reply.decode()}")
            elif action == "Zoom In":
                # Send Ctrl++ to the active window
                result = exec_shell_command("wtype -k ctrl+plus")
                print(f"Zoom in result: {result}")
            elif action == "Zoom Out":
                # Send Ctrl+- to the active window
                result = exec_shell_command("wtype -k ctrl+minus")
                print(f"Zoom out result: {result}")
            elif action == "Actual Size":
                # Send Ctrl+0 to the active window
                result = exec_shell_command("wtype -k ctrl+0")
                print(f"Actual size result: {result}")
            else:
                print(f"No implementation for action: {action}")
        except Exception as e:
            print(f"Error executing command for '{action}': {e}")
