"""
Menu contents definitions for the macOS-style menu bar.
This file contains all the menu items and their organization.
"""


def get_default_menu_contents():
    """Get the default menu contents for all menu items"""
    return {
        "Hyprland": [
            "About Hyprland",
            "Preferences...",
            "---",  # Separator
            "Services",
            "---",
            "Hide Hyprland",
            "Hide Others",
            "Show All",
            "---",
            "Quit Hyprland"
        ],
        "File": [
            "New",
            "Open...",
            "Open Recent",
            "---",
            "Close",
            "Save",
            "Save As...",
            "---",
            "Print..."
        ],
        "Edit": [
            "Undo",
            "Redo",
            "---",
            "Cut",
            "Copy",
            "Paste",
            "---",
            "Select All",
            "Find..."
        ],
        "View": [
            "Show Toolbar",
            "Show Sidebar",
            "---",
            "Zoom In",
            "Zoom Out",
            "Actual Size",
            "---",
            "Full Screen"
        ],
        "Go": [
            "Back",
            "Forward",
            "---",
            "Home",
            "Desktop",
            "Documents",
            "Downloads"
        ],
        "Window": [
            "Move Window to Left",
            "Move Window to Right",
            "Cycle Through Windows",
            "---",
            "Float",
            "Pseudo",
            "Center",
            "---",
            "Group",
            "---",
            "Pin",
            "---",
            "Quit"
        ],
        "Help": [
            "Search",
            "---",
            "Hyprland Help",
            "Keyboard Shortcuts",
            "---",
            "Report a Bug..."
        ]
    }


def get_app_menu_template():
    """Get the template for application-specific menus"""
    return [
        "About {app_name}",
        "Preferences...",
        "---",
        "Services",
        "---",
        "Hide {app_name}",
        "Hide Others", 
        "Show All",
        "---",
        "Quit {app_name}"
    ]


def create_app_menu(app_name):
    """Create a menu for a specific application"""
    template = get_app_menu_template()
    return [item.format(app_name=app_name) if "{app_name}" in item else item for item in template]


def get_menu_contents_for_app(app_name):
    """Get complete menu contents with app-specific first menu"""
    contents = get_default_menu_contents().copy()
    
    if app_name and app_name != "Hyprland":
        # Replace the Hyprland menu with app-specific menu
        contents["Hyprland"] = create_app_menu(app_name)
    
    return contents


# Application-specific menu overrides
APP_SPECIFIC_MENUS = {
    "Firefox": {
        "File": [
            "New Tab",
            "New Window",
            "New Private Window",
            "---",
            "Open File...",
            "Open Location...",
            "---",
            "Close Tab",
            "Close Window",
            "---",
            "Save Page As...",
            "---",
            "Print..."
        ],
        "Edit": [
            "Undo",
            "Redo",
            "---",
            "Cut",
            "Copy",
            "Paste",
            "---",
            "Select All",
            "---",
            "Find in Page...",
            "Find Again",
            "---",
            "Preferences..."
        ],
        "View": [
            "Toolbar",
            "Sidebar",
            "---",
            "Zoom In",
            "Zoom Out",
            "Actual Size",
            "---",
            "Full Screen",
            "---",
            "Page Source",
            "Developer Tools"
        ]
    },
    "Code": {
        "File": [
            "New File",
            "New Window",
            "---",
            "Open File...",
            "Open Folder...",
            "Open Recent",
            "---",
            "Save",
            "Save As...",
            "Save All",
            "---",
            "Close Editor",
            "Close Folder",
            "Close Window"
        ],
        "Edit": [
            "Undo",
            "Redo",
            "---",
            "Cut",
            "Copy",
            "Paste",
            "---",
            "Find",
            "Replace",
            "Find in Files",
            "---",
            "Toggle Comment",
            "Toggle Block Comment"
        ],
        "View": [
            "Command Palette...",
            "Open View...",
            "---",
            "Explorer",
            "Search",
            "Source Control",
            "Debug",
            "Extensions",
            "---",
            "Terminal",
            "Problems",
            "Output",
            "---",
            "Zoom In",
            "Zoom Out",
            "Reset Zoom"
        ]
    }
}


def get_app_specific_menu_contents(app_name):
    """Get menu contents with app-specific overrides"""
    base_contents = get_menu_contents_for_app(app_name)
    
    if app_name in APP_SPECIFIC_MENUS:
        # Override specific menus for this app
        app_menus = APP_SPECIFIC_MENUS[app_name]
        for menu_name, menu_items in app_menus.items():
            base_contents[menu_name] = menu_items
    
    return base_contents
