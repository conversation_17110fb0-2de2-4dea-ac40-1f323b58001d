"""
Menu contents definitions for the macOS-style menu bar.
This file contains all the menu items and their organization.
"""


def get_default_menu_contents():
    """Get the default menu contents for all menu items"""
    return {
        "Hyprland": [
        ],
        "File": [
        ],
        "Edit": [
        ],
        "View": [
            "Full Screen",
            "---",
            "Zoom In",
            "Zoom Out",
            "Actual Size"
        ],
        "Go": [
            "Back",
            "Forward",
            "---",
            # "Home",
            # "Desktop",
            # "Documents",
            # "Downloads"
        ],
        "Window": [
            "Move Window to Left",
            "Move Window to Right",
            "Cycle Through Windows",
            "---",
            "Float",
            "Pseudo",
            "Center",
            "Group",
            "Pin",
            "Quit"
        ],
        "Help": [
            "---",
            "Hyprland Help",
            "Arch Wiki",
            "Keyboard Shortcuts",
            "---",
            "Report a Bug..."
        ]
    }


def get_app_menu_template():
    """Get the template for application-specific menus"""
    return [
        "About {app_name}",
        "---",
        "Hide {app_name}",
        "---",
        "Quit {app_name}"
    ]


def create_app_menu(app_name):
    """Create a menu for a specific application"""
    template = get_app_menu_template()
    return [item.format(app_name=app_name) if "{app_name}" in item else item for item in template]


def get_menu_contents_for_app(app_name):
    """Get complete menu contents with app-specific first menu"""
    contents = get_default_menu_contents().copy()
    
    if app_name and app_name != "Hyprland":
        # Replace the Hyprland menu with app-specific menu
        contents["Hyprland"] = create_app_menu(app_name)
    
    return contents


def get_app_specific_menu_contents(app_name):
    """Get menu contents (now just returns base contents since app-specific overrides are removed)"""
    return get_menu_contents_for_app(app_name)
