import utils.icons as icons
from fabric.system_tray.widgets import SystemTray, SystemTrayItem
from fabric.utils import exec_shell_command
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.centerbox import CenterBox
from fabric.widgets.datetime import DateTime
from fabric.widgets.label import Label
from fabric.widgets.revealer import Revealer
from gi.repository import Gtk, GLib
from modules.panel.components.indicators import Indicators
from modules.panel.components.menubar import MenuBar
from utils.wayland import WaylandWindow as Window

original_do_update_properties = SystemTrayItem.do_update_properties


def patched_do_update_properties(self, *_):
    # Try default GTK theme first
    icon_name = self._item.icon_name
    attention_icon_name = self._item.attention_icon_name

    if self._item.status == "NeedsAttention" and attention_icon_name:
        preferred_icon_name = attention_icon_name
    else:
        preferred_icon_name = icon_name

    # Try to load from default GTK theme
    if preferred_icon_name:
        try:
            default_theme = Gtk.IconTheme.get_default()
            if default_theme.has_icon(preferred_icon_name):
                pixbuf = default_theme.load_icon(
                    preferred_icon_name, self._icon_size, Gtk.IconLookupFlags.FORCE_SIZE
                )
                if pixbuf:
                    self._image.set_from_pixbuf(pixbuf)
                    # Set tooltip
                    tooltip = self._item.tooltip
                    self.set_tooltip_markup(
                        tooltip.description or tooltip.title or self._item.title.title()
                        if self._item.title
                        else "Unknown"
                    )
                    return
        except:
            pass
    original_do_update_properties(self, *_)


SystemTrayItem.do_update_properties = patched_do_update_properties


class SystemDropdownWindow(Window):
    def __init__(self, x=0, y=0, **kwargs):
        super().__init__(
            name="system-dropdown-window",
            title="System Menu Dropdown",
            layer="overlay",  # Use overlay layer to float above everything
            anchor="top left",  # Anchor to top-left corner of screen
            exclusivity="none",  # Don't reserve space
            margin=f"{y}px 0px 0px {x}px",  # Set position directly in constructor
            visible=False,  # Start hidden
            all_visible=True,  # This makes all children visible immediately when shown
            **kwargs
        )

        self.content_box = Box(
            name="system-dropdown-content",
            orientation="v",
            spacing=2,
        )
        self.add(self.content_box)


class SystemActionHandler:
    """Handler class for system actions"""

    def __init__(self):
        pass

    def execute_action(self, action):
        """Execute system actions"""
        try:
            if action == "About This PC":
                # Open system information
                result = exec_shell_command("gnome-system-monitor")
                print(f"About This PC result: {result}")
            elif action == "Force Quit":
                # Open system monitor or task manager
                result = exec_shell_command("gnome-system-monitor")
                print(f"Force Quit result: {result}")
            elif action == "Shutdown":
                # Shutdown the system
                result = exec_shell_command("systemctl poweroff")
                print(f"Shutdown result: {result}")
            elif action == "Restart":
                # Restart the system
                result = exec_shell_command("systemctl reboot")
                print(f"Restart result: {result}")
            elif action == "Sleep":
                # Put system to sleep
                result = exec_shell_command("systemctl suspend")
                print(f"Sleep result: {result}")
            elif action == "Lock":
                # Lock the screen
                result = exec_shell_command("hyprlock")
                print(f"Lock result: {result}")
            else:
                print(f"No implementation for system action: {action}")
        except Exception as e:
            print(f"Error executing system command for '{action}': {e}")


class Panel(Window):
    def __init__(
        self,
    ):
        super().__init__(
            name="bar",
            layer="top",
            anchor="left top right",
            exclusivity="auto",
            visible=False,
        )

        # Initialize system action handler and dropdown state
        self.system_action_handler = SystemActionHandler()
        self.system_dropdown = None

        self.imac = Button(
            name="panel-button",
            child=Label(name="panel-icon", markup=icons.imac),
            on_clicked=self.toggle_system_dropdown
        )
        self.notch_spot = Box(
            name="notch-spot",
            size=(200, 24),
            h_expand=True,
            v_expand=True,
            children=Label(label="notch"),
        )

        self.tray = SystemTray(name="system-tray", spacing=4, icon_size=18)

        self.tray_revealer = Revealer(
            name="tray-revealer",
            child=self.tray,
            child_revealed=False,
            transition_type="slide-left",
            transition_duration=300,
        )

        self.chevron_button = Button(
            name="panel-button",
            child=Label(name="panel-icon", markup=icons.chevron_right),
            on_clicked=self.toggle_tray,
        )

        self.indicators = Indicators()

        self.menubar = MenuBar()

        self.search = Button(
            name="panel-button",
            on_clicked=lambda *_: self.search_apps(),
            child=Label(name="panel-icon", markup=icons.search),
        )

        self.controlcenter = Button(
            name="panel-button",
            child=Label(name="panel-icon", markup=icons.conterpanel),
        )

        self.children = CenterBox(
            name="panel",
            start_children=Box(
                name="modules-left",
                children=[
                    self.imac,
                    self.menubar,
                ],
            ),
            # center_children=Box(
            #     name="modules-center",
            #     children=self.notch_spot,
            # ),
            end_children=Box(
                name="modules-right",
                spacing=4,
                orientation="h",
                children=[
                    self.tray_revealer,
                    self.chevron_button,
                    self.indicators,
                    self.search,
                    self.controlcenter,
                    DateTime(name="date-time", formatters=["%a %b %d %I:%M"]),
                ],
            ),
        )

        return self.show_all()
    
    def search_apps(self):
        cmd = "fabric-cli exec modus 'launcher.show_launcher()'"
        exec_shell_command(cmd)

    def toggle_tray(self, *_):
        current_state = self.tray_revealer.child_revealed
        self.tray_revealer.child_revealed = not current_state

        if self.tray_revealer.child_revealed:
            self.chevron_button.get_child().set_markup(icons.chevron_left)
        else:
            self.chevron_button.get_child().set_markup(icons.chevron_right)

    def toggle_system_dropdown(self, *_):
        """Toggle system dropdown menu visibility"""
        # If dropdown is visible, hide it
        if (self.system_dropdown and self.system_dropdown.get_visible()):
            self.hide_system_dropdown()
            return

        # Show the dropdown
        self.show_system_dropdown()

    def show_system_dropdown(self):
        """Show system dropdown menu"""
        # Hide existing dropdown if any
        if self.system_dropdown:
            self.system_dropdown.set_visible(False)
            self.system_dropdown.destroy()
            self.system_dropdown = None
            # Wait a short moment to ensure the previous dropdown is completely gone
            GLib.timeout_add(150, self._create_and_show_system_dropdown)
        else:
            # No existing dropdown, show immediately
            self._create_and_show_system_dropdown()

    def _create_and_show_system_dropdown(self):
        """Create and show the system dropdown after ensuring previous one is hidden"""
        # Calculate position first
        x, y = self.calculate_system_dropdown_position()

        # Create new dropdown window at the calculated position
        self.system_dropdown = SystemDropdownWindow(x=x, y=y)

        # System menu items
        system_menu_items = [
            "About This PC",
            "---",
            "Force Quit",
            "---",
            "Shutdown",
            "Restart",
            "Sleep",
            "Lock"
        ]

        # Add menu items to dropdown
        for item in system_menu_items:
            if item == "---":
                # Add separator
                separator = Box(
                    name="menu-separator",
                    size=(150, 1),
                    style="background-color: #444; margin: 2px 0;"
                )
                self.system_dropdown.content_box.add(separator)
            else:
                # Add menu item button
                menu_button = Button(
                    name="dropdown-item",
                    child=Label(
                        name="dropdown-label",
                        label=item,
                        h_align="start"  # Align text to the left
                    ),
                    on_clicked=lambda *_, action=item: self.on_system_action(action)
                )
                self.system_dropdown.content_box.add(menu_button)

        # Set dropdown window size
        self.system_dropdown.set_size_request(150, -1)

        # Show the dropdown without animations
        self.system_dropdown.set_visible(True)

        # Return False to stop the timeout
        return False

    def calculate_system_dropdown_position(self):
        """Calculate the position for the system dropdown window"""
        # Get iMac button allocation
        allocation = self.imac.get_allocation()

        print(f"iMac button allocation: x={allocation.x}, y={allocation.y}, width={allocation.width}, height={allocation.height}")

        # Use the exact button position for perfect alignment
        dropdown_x = allocation.x
        dropdown_y = allocation.y + allocation.height + 3  # 3px gap below button

        print(f"System dropdown position: x={dropdown_x}, y={dropdown_y}")

        return dropdown_x, dropdown_y

    def hide_system_dropdown(self):
        """Hide the system dropdown window instantly without animation"""
        if self.system_dropdown:
            # Hide immediately without any transition/animation
            self.system_dropdown.set_visible(False)
            # Destroy the window to free resources
            self.system_dropdown.destroy()
            self.system_dropdown = None

    def on_system_action(self, action):
        """Handle system dropdown menu item clicks"""
        print(f"System action: {action}")
        # Hide dropdown after action
        self.hide_system_dropdown()

        # Handle system actions
        self.system_action_handler.execute_action(action)
