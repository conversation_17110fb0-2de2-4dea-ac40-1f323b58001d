import json
import logging

import cairo
from fabric.hyprland.widgets import get_hyprland_connection
from fabric.utils import (
    exec_shell_command,
    exec_shell_command_async,
    get_relative_path,
    idle_add,
    remove_handler,
)
from fabric.utils.helpers import get_desktop_applications
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.eventbox import EventBox
from fabric.widgets.image import Image
from fabric.widgets.revealer import Revealer
from gi.repository import Gdk, GLib, Gtk

import config.data as data
from modules.corners import MyCorner
from utils.icon_resolver import IconResolver
from utils.occlusion import check_occlusion
from utils.wayland import WaylandWindow as Window


def read_config():
    """Read and return the full configuration from the JSON file, handling missing file."""
    config_path = get_relative_path("../config/dock.json")
    try:
        with open(config_path, "r") as file:
            config_data = json.load(file)

        if (
            "pinned_apps" in config_data
            and config_data["pinned_apps"]
            and isinstance(config_data["pinned_apps"][0], str)
        ):
            all_apps = get_desktop_applications()
            app_map = {app.name: app for app in all_apps if app.name}

            old_pinned = config_data["pinned_apps"]
            config_data["pinned_apps"] = []

            for app_id in old_pinned:
                app = app_map.get(app_id)
                if app:
                    app_data_obj = {
                        "name": app.name,
                        "display_name": app.display_name,
                        "window_class": app.window_class,
                        "executable": app.executable,
                        "command_line": app.command_line,
                    }
                    config_data["pinned_apps"].append(app_data_obj)
                else:
                    config_data["pinned_apps"].append({"name": app_id})

    except (FileNotFoundError, json.JSONDecodeError):
        config_data = {"pinned_apps": []}
    return config_data


def createSurfaceFromWidget(widget: Gtk.Widget) -> cairo.ImageSurface:
    alloc = widget.get_allocation()
    surface = cairo.ImageSurface(
        cairo.Format.ARGB32,
        alloc.width,
        alloc.height,
    )
    cr = cairo.Context(surface)
    cr.set_source_rgba(255, 255, 255, 0)
    cr.rectangle(0, 0, alloc.width, alloc.height)
    cr.fill()
    widget.draw(cr)
    return surface


class Dock(Window):
    _instances = []

    def __init__(self, integrated_mode: bool = False, **kwargs):
        self.integrated_mode = integrated_mode
        self.icon_size = 20 if self.integrated_mode else data.DOCK_ICON_SIZE
        self.effective_occlusion_size = 36 + self.icon_size

        # Always show dock at bottom, horizontal
        anchor_to_set = "bottom"
        revealer_transition_type = "slide-up"
        main_box_orientation_val = Gtk.Orientation.VERTICAL
        main_box_h_align_val = "center"
        dock_wrapper_orientation_val = Gtk.Orientation.HORIZONTAL
        self.actual_dock_is_horizontal = True

        super().__init__(
            name="dock-window",
            layer="top",
            anchor=anchor_to_set,
            margin="0px 0px 0px 0px",
            exclusivity="none",
            **kwargs,
        )
        Dock._instances.append(self)

        self.config = read_config()
        self.conn = get_hyprland_connection()
        self.icon_resolver = IconResolver()
        self.pinned = self.config.get("pinned_apps", [])
        self.config_path = get_relative_path("../config/dock.json")
    def _build_app_identifiers_map(self):
        """
        Build a mapping from app names to app objects for quick lookup.
        """
        app_map = {}
        for app in self._all_apps:
            if hasattr(app, 'name') and app.name:
                app_map[app.name] = app
        return app_map

        self.app_map = {}
        self._all_apps = get_desktop_applications()
        self.app_identifiers = self._build_app_identifiers_map()

        self.hide_id = None
        self._arranger_handler = None
        self._drag_in_progress = False
        self.always_occluded = (
            data.DOCK_ALWAYS_OCCLUDED if not self.integrated_mode else False
        )
        self.is_mouse_over_dock_area = False
        self._prevent_occlusion = False

        self.view = Box(name="viewport", spacing=4)
        self.wrapper = Box(
            name="dock",
            children=[self.view],
        )

        self.wrapper.set_orientation(dock_wrapper_orientation_val)
        self.view.set_orientation(dock_wrapper_orientation_val)

        if self.integrated_mode:
            self.wrapper.add_style_class("integrated")
        else:
            self.wrapper.remove_style_class("vertical")
            match data.DOCK_THEME:
                case "Pills":
                    self.wrapper.add_style_class("pills")
                case "Dense":
                    self.wrapper.add_style_class("dense")
                case "Edge":
                    self.wrapper.add_style_class("edge")
                case _:
                    self.wrapper.add_style_class("pills")

        if not self.integrated_mode:
            self.dock_eventbox = EventBox()
            self.dock_eventbox.add(self.wrapper)
            self.dock_eventbox.connect("enter-notify-event", self._on_dock_enter)
            self.dock_eventbox.connect("leave-notify-event", self._on_dock_leave)

            self.corner_left = Box(
                name="dock-corner-left",
                orientation=Gtk.Orientation.VERTICAL,
                h_align="start",
                children=[
                    Box(v_expand=True, v_align="fill"),
                    MyCorner("bottom-right"),
                ],
            )
            self.corner_right = Box(
                name="dock-corner-right",
                orientation=Gtk.Orientation.VERTICAL,
                h_align="end",
                children=[
                    Box(v_expand=True, v_align="fill"),
                    MyCorner("bottom-left"),
                ],
            )
            self.dock_full = Box(
                name="dock-full",
                orientation=Gtk.Orientation.HORIZONTAL,
                h_expand=True,
                h_align="fill",
                children=[self.corner_left, self.dock_eventbox, self.corner_right],
            )

            self.dock_revealer = Revealer(
                name="dock-revealer",
                transition_type=revealer_transition_type,
                transition_duration=250,
                child_revealed=False,
                child=self.dock_full,
            )

            self.hover_activator = EventBox()
            self.hover_activator.set_size_request(-1, 1)
            self.hover_activator.connect("enter-notify-event", self._on_hover_enter)
            self.hover_activator.connect("leave-notify-event", self._on_hover_leave)

            self.main_box = Box(
                orientation=main_box_orientation_val,
                children=[self.hover_activator, self.dock_revealer],
                h_align=main_box_h_align_val,
            )
            self.add(self.main_box)

            if data.DOCK_THEME in ["Edge", "Dense"]:
                for corner in [self.corner_left, self.corner_right]:
                    corner.set_visible(False)

            if not data.DOCK_ENABLED:
                self.set_visible(False)

            if self.always_occluded:
                self.dock_full.add_style_class("occluded")

        self.view.drag_source_set(
            Gdk.ModifierType.BUTTON1_MASK,
            [Gtk.TargetEntry.new("text/plain", Gtk.TargetFlags.SAME_APP, 0)],
            Gdk.DragAction.MOVE,
        )
        self.view.drag_dest_set(
            Gtk.DestDefaults.ALL,
            [Gtk.TargetEntry.new("text/plain", Gtk.TargetFlags.SAME_APP, 0)],
            Gdk.DragAction.MOVE,
        )
        self.view.connect("drag-data-get", self.on_drag_data_get)
        self.view.connect("drag-data-received", self.on_drag_data_received)
        self.view.connect("drag-begin", self.on_drag_begin)
        self.view.connect("drag-end", self.on_drag_end)

        if self.conn.ready:
            self.update_dock()
            if not self.integrated_mode:
                GLib.timeout_add(250, self.check_occlusion_state)
        else:
            self.conn.connect("event::ready", self.update_dock)
            if not self.integrated_mode:
                self.conn.connect(
                    "event::ready",
                    lambda *args: GLib.timeout_add(250, self.check_occlusion_state),
                )

        for ev in ("activewindow", "openwindow", "closewindow", "changefloatingmode"):
            self.conn.connect(f"event::{ev}", self.update_dock)

        if not self.integrated_mode:
            self.conn.connect("event::workspace", self.check_hide)

        GLib.timeout_add_seconds(1, self.check_config_change)

    def check_occlusion_state(self):
        """
        Check if the dock should be occluded (hidden) based on window state or other conditions.
        """
        try:
            occluded = check_occlusion(self)
            if occluded and not self._prevent_occlusion:
                self.set_visible(False)
            else:
                self.set_visible(True)
        except Exception as e:
            logging.error(f"Error checking occlusion state: {e}")
        return True  # For GLib timeout continuation

    # ... rest of the class remains unchanged ...
