:vars {
    --background: #150d16;
    --foreground: #f5dbc4;
    --color0: #150d16;
    --color1: #72448D;
    --color2: #9C5995;
    --color3: #D5719F;
    --color4: #9A62F3;
    --color5: #E075DF;
    --color6: #F98F9F;
    --color7: #f5dbc4;
    --color8: #ab9989;
    --color9: #72448D;
    --color10: #9C5995;
    --color11: #D5719F;
    --color12: #9A62F3;
    --color13: #E075DF;
    --color14: #F98F9F;
    --color15: #f5dbc4;
    --window-bg: alpha(var(--background), 0.6);
    --module-bg: alpha(var(--background), 0.4);
    --border-color: var(--color11);
}

* {
    all: unset;
    font-family: "Jost*";
    font-weight: 500;
    font-size: 18px;
    color: var(--foreground);
}

#window-inner {
    border: solid 2px;
    border-color: var(--color11);
    background-color: var(--window-bg);
    border-radius: 100px;
    border-radius: 12px;
    padding: 10px;
}

#date-time>label {
    font-weight: 900;
    font-size: 28px;
}

#header {
    border: solid 1px var(--border-color);
    border-radius: 8px;
    box-shadow: 0px 18px 23px -6px rgba(0, 0, 0, 0.75);
    background: linear-gradient(90deg,
            alpha(var(--color11), 0.2),
            alpha(var(--background), 0.2),
            var(--module-bg));
}

#profile-pic {
    background-color: alpha(var(--color11), 0.2);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 6px;
    padding: 32px;
    margin: 4px;
}

#progress-bar {
    color: var(--module-bg);
    border: solid 10px var(--border-color);
}

#progress-bar-sep {
    padding: 4px;
    margin: 16px 0px;
    border-radius: 100px;
    background-color: var(--module-bg);
}

#progress-bar-container {
    background-color: var(--module-bg);
    border-radius: 100px;
    padding: 8px;
}
