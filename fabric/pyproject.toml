[project]
name = "fabric"
version = "0.0.2"
dependencies = ["click", "loguru", "pycairo", "PyGObject==3.50.0"]
requires-python = ">=3.11"
authors = [
  { name = "<PERSON><PERSON>", email = "<EMAIL>" },
  { name = "<PERSON> Read", email = "<EMAIL>" },
]
maintainers = [
  { name = "<PERSON><PERSON>", email = "<EMAIL>" },
  { name = "Kai Read", email = "<EMAIL>" },
]
description = "Next-Gen python framework for creating system widgets on *Nix systems!"
readme = "README.md"
keywords = [
  "widgets",
  "gtk",
  "linux",
  "pygobject",
  "bar",
  "statusbar",
  "panel",
  "dock",
]
classifiers = ["Programming Language :: Python"]

[project.optional-dependencies]
system-status = ["psutil"]

[tool.setuptools.package-data]
"*" = ["*.xml", "*.js"]

[project.urls]
"Website" = "https://ffpy.org"
"Documentation" = "https://wiki.ffpy.org"
"Website (mirror)" = "https://fabric-widgets.org"
"Documentation (mirror)" = "https://wiki.fabric-widgets.org"
"Source" = "https://github.com/Fabric-Development/fabric"
"Bug Tracker" = "https://github.com/Fabric-Development/fabric/issues"
"Changelog" = "https://github.com/Fabric-Development/fabric/blob/master/CHANGELOG.md"
