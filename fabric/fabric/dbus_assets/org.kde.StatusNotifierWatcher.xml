<node>
	<interface name="org.kde.StatusNotifierWatcher">
		<annotation name="org.gtk.GDBus.C.Name" value="Watcher" />
		<method name="RegisterStatusNotifierItem">
			<annotation name="org.gtk.GDBus.C.Name" value="RegisterItem" />
			<arg name="service" type="s" direction="in" />
		</method>
		<method name="RegisterStatusNotifierHost">
			<annotation name="org.gtk.GDBus.C.Name" value="RegisterHost" />
			<arg name="service" type="s" direction="in" />
		</method>
		<property name="RegisteredStatusNotifierItems" type="as" access="read">
			<annotation name="org.gtk.GDBus.C.Name" value="RegisteredItems" />
			<annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="QStringList" />
		</property>
		<property name="IsStatusNotifierHostRegistered" type="b" access="read">
			<annotation name="org.gtk.GDBus.C.Name" value="IsHostRegistered" />
		</property>
		<property name="ProtocolVersion" type="i" access="read" />
		<signal name="StatusNotifierItemRegistered">
			<annotation name="org.gtk.GDBus.C.Name" value="ItemRegistered" />
			<arg type="s" direction="out" name="service" />
		</signal>
		<signal name="StatusNotifierItemUnregistered">
			<annotation name="org.gtk.GDBus.C.Name" value="ItemUnregistered" />
			<arg type="s" direction="out" name="service" />
		</signal>
		<signal name="StatusNotifierHostRegistered">
			<annotation name="org.gtk.GDBus.C.Name" value="HostRegistered" />
		</signal>
		<signal name="StatusNotifierHostUnregistered">
			<annotation name="org.gtk.GDBus.C.Name" value="HostUnregistered" />
		</signal>
	</interface>
</node>
