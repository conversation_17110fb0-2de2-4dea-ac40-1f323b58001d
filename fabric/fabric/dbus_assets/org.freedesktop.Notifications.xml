<node>
	<interface name="org.freedesktop.Notifications">
		<signal name="NotificationClosed">
			<arg name="id" type="u" direction="out" />
			<arg name="reason" type="u" direction="out" />
		</signal>
		<signal name="ActionInvoked">
			<arg name="id" type="u" direction="out" />
			<arg name="action_key" type="s" direction="out" />
		</signal>
		<method name="Notify">
			<annotation name="org.qtproject.QtDBus.QtTypeName.In6" value="QVariantMap" />
			<arg type="u" direction="out" />
			<arg name="app_name" type="s" direction="in" />
			<arg name="replaces_id" type="u" direction="in" />
			<arg name="app_icon" type="s" direction="in" />
			<arg name="summary" type="s" direction="in" />
			<arg name="body" type="s" direction="in" />
			<arg name="actions" type="as" direction="in" />
			<arg name="hints" type="a{sv}" direction="in" />
			<arg name="timeout" type="i" direction="in" />
		</method>
		<method name="CloseNotification">
			<arg name="id" type="u" direction="in" />
		</method>
		<method name="GetCapabilities">
			<arg type="as" name="caps" direction="out" />
		</method>
		<method name="GetServerInformation">
			<arg type="s" name="name" direction="out" />
			<arg type="s" name="vendor" direction="out" />
			<arg type="s" name="version" direction="out" />
			<arg type="s" name="spec_version" direction="out" />
		</method>
	</interface>
</node>
