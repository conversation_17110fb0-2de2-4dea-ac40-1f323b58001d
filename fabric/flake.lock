{"nodes": {"nixpkgs": {"locked": {"lastModified": 1733261153, "narHash": "sha256-eq51hyiaIwtWo19fPEeE0Zr2s83DYMKJoukNLgGGpek=", "owner": "NixOS", "repo": "nixpkgs", "rev": "b681065d0919f7eb5309a93cea2cfa84dec9aa88", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-24.11", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"nixpkgs": "nixpkgs", "utils": "utils"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}}, "root": "root", "version": 7}