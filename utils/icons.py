# Parameters
font_family: str = "tabler-icons"
font_weight: str = "normal"

span: str = f"<span font-family='{font_family}' font-weight='{font_weight}'>"

# Panels
imac: str = "&#xfd74;"

# Chevrons
chevron_up: str = "&#xea62;"
chevron_down: str = "&#xea5f;"
chevron_left: str = "&#xea60;"
chevron_right: str = "&#xea61;"

# Battery
bat_charging: str = "&#xea33;"
bat_alert: str = "&#xff1d;"
bat_plugged: str = "&#xef3b;"
battery_full: str = "&#xf668;"
battery_0: str = "&#xea2f;"
battery_1: str = "&#xea30;"
battery_2: str = "&#xea31;"

# Power Manager
power_saving: str = "&#xed4f;"
power_balanced: str = "&#xfa77;"
power_performance: str = "&#xec45;"

# Network
wifi_0: str = "&#xeba3;"
wifi_1: str = "&#xeba4;"
wifi_2: str = "&#xeba5;"
wifi_3: str = "&#xeb52;"
world: str = "&#xeb54;"
world_off: str = "&#xf1ca;"
wifi_off: str = "&#xecfa;"

# Bluetooth
bluetooth: str = "&#xea37;"
bluetooth_off: str = "&#xeceb;"
bluetooth_connected: str = "&#xecea;"
bluetooth_disconnected: str = "&#xf081;"

# Misc
loader: str = "&#xeca3;"
search: str = "&#xeb1c;"
conterpanel: str = "&#xec38;"

exceptions: list[str] = ["font_family", "font_weight", "span"]


def apply_span() -> None:
    global_dict = globals()
    for key in global_dict:
        if key not in exceptions and not key.startswith("__"):
            global_dict[key] = f"{span}{global_dict[key]}</span>"


apply_span()
